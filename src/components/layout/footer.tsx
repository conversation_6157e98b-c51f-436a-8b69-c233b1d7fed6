import { Clock, FileText, Globe, Linkedin, Mail, MapPin, MessageCircle, Phone } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface FooterProps {
  locale: string;
}

export function Footer({ locale }: FooterProps) {
  const t = useTranslations('web.header');
  const footerT = useTranslations('web.footer');

  // 实际存在的页面链接
  const quickLinks = [
    {
      title: t('blog'),
      href: `/${locale}/blog`,
    },
    {
      title: t('about'),
      href: `/${locale}/about`,
    },
    {
      title: footerT('links.contact'),
      href: `/${locale}/contact`,
    },
  ];

  // 产品相关（实际存在的）
  const productLinks = [
    {
      title: t('product.center'),
      href: `/${locale}/products`,
    },
    {
      title: footerT('links.productDocs'),
      href: `/${locale}/products`,
    },
    {
      title: footerT('links.solutions'),
      href: `/${locale}#solutions`,
    },
  ];

  // 公司信息
  const companyInfo = [
    {
      icon: MapPin,
      label: footerT('company.address.label'),
      value: footerT('company.address.value'),
    },
    {
      icon: Phone,
      label: footerT('company.phone.label'),
      value: footerT('company.phone.value'),
      href: 'tel:+86-************',
    },
    {
      icon: Mail,
      label: footerT('company.email.label'),
      value: footerT('company.email.value'),
      href: 'mailto:<EMAIL>',
    },
    {
      icon: Clock,
      label: footerT('company.hours.label'),
      value: footerT('company.hours.value'),
    },
  ];

  // 社交媒体
  const socialMedia = [
    {
      icon: MessageCircle,
      label: footerT('social.wechat'),
      href: '#',
    },
    {
      icon: Linkedin,
      label: footerT('social.linkedin'),
      href: 'https://linkedin.com/company/tucsenberg',
    },
    {
      icon: Globe,
      label: footerT('social.website'),
      href: `/${locale}`,
    },
    {
      icon: FileText,
      label: footerT('social.blog'),
      href: `/${locale}/blog`,
    },
  ];

  // 法律页面
  const legalPages = [
    {
      title: footerT('legal.privacy'),
      href: `/${locale}/legal/privacy`,
    },
    {
      title: footerT('legal.terms'),
      href: `/${locale}/legal/terms`,
    },
    {
      title: footerT('legal.contact'),
      href: `/${locale}/contact`,
    },
  ];

  // 页脚内容组件
  const FooterContent = () => (
    <div className='grid gap-12 lg:grid-cols-3'>
      {/* 公司品牌和简介 */}
      <div className='flex flex-col gap-6'>
        <div className='flex flex-col gap-4'>
          <h2 className='text-2xl font-semibold tracking-tight'>
            FloodControl
          </h2>
          <p className='text-muted-foreground text-sm leading-relaxed'>
            {footerT('company.description')}
          </p>
        </div>

        {/* 社交媒体 */}
        <div className='flex flex-col gap-3'>
          <h3 className='text-sm font-medium'>{footerT('social.title')}</h3>
          <div className='flex gap-2'>
            {socialMedia.map((social) => {
              const Icon = social.icon;
              return (
                <Link
                  key={social.label}
                  href={social.href}
                  className='inline-flex h-9 w-9 items-center justify-center rounded-md bg-background border hover:bg-accent hover:text-accent-foreground transition-colors'
                  target={social.href.startsWith('http') ? '_blank' : undefined}
                  rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  title={social.label}
                >
                  <Icon className='h-4 w-4' />
                </Link>
              );
            })}
          </div>
        </div>

        {/* 版权信息 */}
        <div className='pt-4 border-t border-border'>
          <p className='text-xs text-muted-foreground'>
            {footerT('copyright', { year: new Date().getFullYear() })}
          </p>
        </div>
      </div>

      {/* 快速链接 */}
      <div className='flex flex-col gap-4'>
        <h3 className='text-sm font-medium'>{footerT('links.title')}</h3>
        <div className='flex flex-col gap-2'>
          {quickLinks.map((link) => (
            <Link
              key={link.title}
              href={link.href}
              className='text-sm text-muted-foreground hover:text-foreground transition-colors rounded px-2 py-1 -mx-2 hover:bg-accent'
            >
              {link.title}
            </Link>
          ))}
        </div>

        {/* 产品相关 */}
        <div className='flex flex-col gap-3 mt-4'>
          <h4 className='text-sm font-medium'>{footerT('products.title')}</h4>
          <div className='flex flex-col gap-2'>
            {productLinks.map((link) => (
              <Link
                key={link.title}
                href={link.href}
                className='text-sm text-muted-foreground hover:text-foreground transition-colors rounded px-2 py-1 -mx-2 hover:bg-accent'
              >
                {link.title}
              </Link>
            ))}
          </div>
        </div>

        {/* 法律信息 */}
        <div className='flex flex-col gap-3 mt-4'>
          <h4 className='text-sm font-medium'>{footerT('legal.title')}</h4>
          <div className='flex flex-col gap-2'>
            {legalPages.map((page) => (
              <Link
                key={page.title}
                href={page.href}
                className='text-sm text-muted-foreground hover:text-foreground transition-colors rounded px-2 py-1 -mx-2 hover:bg-accent'
              >
                {page.title}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* 联系信息 */}
      <div className='flex flex-col gap-4'>
        <h3 className='text-sm font-medium'>{footerT('company.title')}</h3>
        <div className='flex flex-col gap-3'>
          {companyInfo.map((info) => {
            const Icon = info.icon;
            const content = (
              <div className='flex items-start gap-3 text-sm'>
                <Icon className='h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0' />
                <div className='flex flex-col gap-1'>
                  <span className='font-medium'>{info.label}</span>
                  <span className='text-muted-foreground'>{info.value}</span>
                </div>
              </div>
            );

            return info.href ? (
              <Link
                key={info.label}
                href={info.href}
                className='rounded px-2 py-1 -mx-2 hover:bg-accent transition-colors'
              >
                {content}
              </Link>
            ) : (
              <div key={info.label} className='px-2 py-1'>
                {content}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  return (
    <section className='py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='rounded-lg border bg-card p-8 lg:p-12 shadow-sm'>
          <FooterContent />
        </div>
      </div>
    </section>
  );
}
