import { Linkedin, Mail, MapPin, MessageCircle, Phone } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface FooterProps {
  locale: string;
}

export function Footer({ locale }: FooterProps) {
  const footerT = useTranslations('web.footer');

  // 公司联系信息
  const contactInfo = [
    {
      icon: MapPin,
      label: footerT('company.address.label'),
      value: footerT('company.address.value'),
    },
    {
      icon: Phone,
      label: footerT('company.phone.label'),
      value: footerT('company.phone.value'),
      href: 'tel:+86-************',
    },
    {
      icon: Mail,
      label: footerT('company.email.label'),
      value: footerT('company.email.value'),
      href: 'mailto:<EMAIL>',
    },
  ];

  // 社交媒体
  const socialMedia = [
    {
      icon: MessageCircle,
      label: footerT('social.wechat'),
      href: '#',
      description: footerT('social.wechatDesc'),
    },
    {
      icon: Linkedin,
      label: footerT('social.linkedin'),
      href: 'https://linkedin.com/company/tucsenberg',
      description: footerT('social.linkedinDesc'),
    },
  ];

  return (
    <section className='py-16 lg:py-24'>
      <div className='container mx-auto'>
        {/* 主要内容区域 */}
        <div className='rounded-2xl border bg-card/50 backdrop-blur-sm p-8 lg:p-12 shadow-lg hover:shadow-xl transition-all duration-500 group'>
          <div className='grid gap-12 lg:grid-cols-2 xl:grid-cols-3'>

            {/* 品牌与简介 */}
            <div className='xl:col-span-1 flex flex-col gap-8'>
              <div className='space-y-4'>
                <div className='flex items-center gap-3'>
                  <div className='h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center'>
                    <div className='h-4 w-4 rounded bg-primary'></div>
                  </div>
                  <h2 className='text-2xl font-bold tracking-tight'>
                    FloodControl
                  </h2>
                </div>
                <p className='text-muted-foreground leading-relaxed max-w-md'>
                  {footerT('company.description')}
                </p>
              </div>

              {/* 社交媒体 */}
              <div className='space-y-4'>
                <h3 className='text-sm font-semibold text-foreground/80 uppercase tracking-wider'>
                  {footerT('social.title')}
                </h3>
                <div className='flex gap-3'>
                  {socialMedia.map((social) => {
                    const Icon = social.icon;
                    return (
                      <Link
                        key={social.label}
                        href={social.href}
                        className='group/social relative inline-flex h-11 w-11 items-center justify-center rounded-xl bg-background/80 border border-border/50 hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 hover:scale-105 hover:shadow-md'
                        target={social.href.startsWith('http') ? '_blank' : undefined}
                        rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                        title={social.label}
                      >
                        <Icon className='h-5 w-5 text-muted-foreground group-hover/social:text-primary transition-colors duration-300' />
                        <div className='absolute -top-12 left-1/2 -translate-x-1/2 px-2 py-1 bg-foreground text-background text-xs rounded opacity-0 group-hover/social:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap'>
                          {social.label}
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* 联系信息 */}
            <div className='lg:col-span-1 xl:col-span-2 grid gap-8 md:grid-cols-2 xl:grid-cols-1'>
              <div className='space-y-6'>
                <h3 className='text-sm font-semibold text-foreground/80 uppercase tracking-wider'>
                  {footerT('company.title')}
                </h3>
                <div className='space-y-4'>
                  {contactInfo.map((info) => {
                    const Icon = info.icon;
                    const content = (
                      <div className='group/contact flex items-start gap-4 p-3 rounded-xl hover:bg-accent/50 transition-all duration-300'>
                        <div className='flex-shrink-0 h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover/contact:bg-primary/20 transition-colors duration-300'>
                          <Icon className='h-5 w-5 text-primary' />
                        </div>
                        <div className='flex-1 min-w-0'>
                          <p className='text-sm font-medium text-foreground/90 mb-1'>
                            {info.label}
                          </p>
                          <p className='text-sm text-muted-foreground leading-relaxed break-words'>
                            {info.value}
                          </p>
                        </div>
                      </div>
                    );

                    return info.href ? (
                      <Link
                        key={info.label}
                        href={info.href}
                        className='block hover:scale-[1.02] transition-transform duration-300'
                      >
                        {content}
                      </Link>
                    ) : (
                      <div key={info.label}>
                        {content}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* 底部分割线和版权 */}
          <div className='mt-12 pt-8 border-t border-border/50'>
            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
              <p className='text-xs text-muted-foreground'>
                {footerT('copyright', { year: new Date().getFullYear() })}
              </p>
              <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                <span className='hidden sm:inline'>•</span>
                <span>{footerT('company.slogan')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
}
